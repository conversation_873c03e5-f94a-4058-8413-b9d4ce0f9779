from functools import partial
import warnings

from kitchen.configs import routing
from kitchen.configs.naming import get_node_name
from kitchen.plotter.plotting_manual import CHECK_PLOT_MANUAL, PlotManual
from kitchen.settings.timeline import ALIGNMENT_STYLE
from kitchen.structure.hierarchical_data_structure import Fov, Node, Session, DataSet
from kitchen.plotter.decorators.default_decorators import default_style
from kitchen.plotter.ax_plotter.basic_plot import flat_view, stack_view
from kitchen.operator.select_trials import select_predefined_trial_types
from kitchen.utils.sequence_kit import select_from_value


def session_overview(
        session_node: Session,
        
        plot_manual: PlotManual,
):
    """ Flat view of a session node """

    session_name = get_node_name(session_node)
    session_dataset  = DataSet(name=session_name, nodes=[session_node])
    default_style(
        mosaic_style=[[session_name,],],
        content_dict={
            session_name: (
                partial(flat_view, plot_manual=plot_manual),
                session_dataset)
            },
        figsize=(5, 2),
        save_path=routing.default_fig_path(session_dataset, "SessionOverview_{}.png"),
    )


def fov_overview(
        fov_node: Fov,
        dataset: DataSet,
        
        plot_manual: <PERSON>lotManual,
):
    """ Flat view of a fov node """

    session_nodes  = dataset.subtree(fov_node).select("session")
    n_session = len(session_nodes)
    default_style(
        mosaic_style=[[get_node_name(session_node),] for session_node in session_nodes],
        content_dict={
            get_node_name(session_node): (
                partial(flat_view, plot_manual=plot_manual),
                DataSet(name=session_node.session_id, nodes=[session_node])) 
            for session_node in session_nodes
            },
        figsize=(5, 2 * n_session),
        save_path=routing.default_fig_path(session_nodes, "FovOverview_{}.png"),
    )


def node_trial_avg_default(
        node: Node,
        dataset: DataSet,
        
        plot_manual: PlotManual,
):
    """Stack view of all trials in the subtree of a node, plotted in a row"""
    subtree = dataset.subtree(node)

    # filter out trial types that cannot be plotted
    all_possible_trial_types = select_predefined_trial_types(subtree)
    trial_types = select_from_value(all_possible_trial_types,
                                    _self = lambda dataset: CHECK_PLOT_MANUAL(dataset, plot_manual))
    
    n_trial_types = len(trial_types)
    for alignment_name, alignment_events in ALIGNMENT_STYLE.items():
        try:
            default_style(
                mosaic_style=[[f"{get_node_name(node)}\n{trial_type}"
                            for trial_type in trial_types.keys()],],
                content_dict={
                    f"{get_node_name(node)}\n{trial_type}": (
                        partial(stack_view, plot_manual=plot_manual, sync_events=alignment_events),
                        trial_dataset) 
                    for trial_type, trial_dataset in trial_types.items()
                    },
                figsize=(n_trial_types * 1.5, 1.5),
                save_path=routing.default_fig_path(subtree, f"TrialAvg_{{}}_{alignment_name}.png"),
            )
        except Exception as e:
            warnings.warn(f"Cannot plot trial average for {get_node_name(node)} with {alignment_name}: {e}")


def fov_trial_avg_default(
        fov_node: Fov,
        dataset: DataSet,
        
        plot_manual: PlotManual,
):
    """ Each day in a row, plot all trial types """

    max_n_trial_types = 0
    fovday_nodes = dataset.subtree(fov_node).select("fovday")
    for alignment_name, alignment_events in ALIGNMENT_STYLE.items():
        try:
            total_mosaic, content_dict = [], {}
            for fovday_node in fovday_nodes:
                subtree = dataset.subtree(fovday_node)
                all_possible_trial_types = select_predefined_trial_types(subtree)

                # filter out trial types that cannot be plotted
                trial_types = select_from_value(all_possible_trial_types,
                                                _self = lambda dataset: CHECK_PLOT_MANUAL(dataset, plot_manual))

                # update max_n_trial_types
                max_n_trial_types = max(max_n_trial_types, len(trial_types)) 

                # update total_mosaic and content_dict
                total_mosaic.append([f"{get_node_name(fovday_node)}\n{trial_type}"
                                    for trial_type in trial_types.keys()])
                content_dict.update({
                    f"{get_node_name(fovday_node)}\n{trial_type}": (
                        partial(stack_view, plot_manual=plot_manual, sync_events=alignment_events),
                        trial_dataset) 
                    for trial_type, trial_dataset in trial_types.items()
                    })

            for i in range(len(total_mosaic)):
                total_mosaic[i] += ["."] * (max_n_trial_types - len(total_mosaic[i]))

            default_style(
                mosaic_style=total_mosaic,
                content_dict=content_dict,
                figsize=(max_n_trial_types * 1.5, 1.5 * len(total_mosaic)),
                save_path=routing.default_fig_path(dataset.subtree(fov_node), f"TrialAvg_{{}}_{alignment_name}.png"),
            )
        except Exception as e:
            warnings.warn(f"Cannot plot trial average for {get_node_name(fov_node)} with {alignment_name}: {e}")

    
